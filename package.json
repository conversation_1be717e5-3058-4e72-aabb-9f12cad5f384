{"name": "swagger-mcp", "version": "1.0.0", "description": "Fetch Swagger definition from a URL", "type": "module", "bin": {"swagger-mcp": "./build/index.js"}, "scripts": {"build": "tsc && node -e \"require('fs').copyFileSync('.env', 'build/.env'); require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "inspector": "tsc && node -e \"require('fs').copyFileSync('.env', 'build/.env'); require('fs').chmodSync('build/index.js', '755')\" && npx @modelcontextprotocol/inspector build/index.js", "test": "npx tsx tests/run-all-tests.ts"}, "files": ["build"], "repository": {"type": "git", "url": "git+https://github.com/readingdancer/swagger-mcp.git"}, "keywords": ["swagger", "api", "mcp", "server", "openapi", "generation"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/readingdancer/swagger-mcp/issues"}, "homepage": "https://github.com/readingdancer/swagger-mcp#readme", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@types/js-yaml": "^4.0.9", "@types/minimist": "^1.2.5", "axios": "^1.8.3", "dotenv": "^16.4.7", "js-yaml": "^4.0.9", "minimist": "^1.2.8", "winston": "^3.12.0"}, "devDependencies": {"@types/express": "^5.0.0", "@types/node": "^22.13.10", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.2"}}