# Swagger MCP Prompts

This document contains useful prompts that can be used with AI assistants to guide them through common tasks with the Swagger MCP tools.

## Adding a New Endpoint

The following prompt describes the exact process required to add a new endpoint using the Swagger MCP tools. You can provide this to an AI assistant to guide it through the process step by step.

```To add a new endpoint using the Swagger MCP tools, please follow these exact steps in order:

1. First, use the `mcp__getSwaggerDefinition` tool to download and save the Swagger definition if you haven't already:
   - Provide the URL of the Swagger definition
   - Specify the save location (usually the current solution's root folder)
   - After receiving the response, create a `.swagger-mcp` file in the root directory with the content: `SWAGGER_FILEPATH=TheFullFilePath`

2. Next, use the `mcp__listEndpoints` tool to explore available endpoints:
   - Use the Swagger file path from the `.swagger-mcp` file
   - Review the list to find the endpoint you want to implement or to understand the API structure

3. For the endpoint you want to implement, use the `mcp__listEndpointModels` tool:
   - Specify the path and HTTP method of the endpoint
   - Use the Swagger file path from the `.swagger-mcp` file
   - This will show you all models used by this endpoint

4. For each model identified in the previous step, use the `mcp__generateModelCode` tool:
   - Specify the model name exactly as shown in the previous step
   - Use the Swagger file path from the `.swagger-mcp` file
   - Save the generated TypeScript code to an appropriate file in your project

5. Finally, use the `mcp__generateEndpointToolCode` tool to create the MCP tool definition:
   - Specify the path and HTTP method of the endpoint
   - Use the Swagger file path from the `.swagger-mcp` file
   - Optionally configure naming options (includeApiInName, includeVersionInName, singularizeResourceNames)
   - Save the generated tool definition to an appropriate file in your project

6. Implement any additional logic needed for the tool handler, such as:
   - Authentication handling
   - Error handling
   - File upload/download support if needed
   - Integration with your application's services

7. Register the new tool in your MCP server configuration

8. Test the new endpoint with sample requests to ensure it works correctly

Please follow these steps in order, as each step depends on the output of the previous steps.
```

## Using Generated Models

This prompt guides through the process of using the generated models in your application:

```To use the models generated by the Swagger MCP tools in your application, please follow these steps:

1. First, ensure you have generated the model code using the `mcp__generateModelCode` tool
2. Save the generated TypeScript code to a file in your models directory
3. Import the model in your application code where needed
4. Use the model for type checking, validation, and data manipulation
5. If you need to extend the model with additional functionality, create a class that implements the interface

Remember that the generated models are TypeScript interfaces, which provide type checking at compile time but don't include runtime validation. If you need runtime validation, consider using a validation library like Zod, Joi, or class-validator with the generated models.
```

Feel free to add more prompts to this file as you develop more complex workflows with the Swagger MCP tools.
