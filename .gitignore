# Dependency directories
node_modules/
jspm_packages/

# Build output
build/
dist/
lib/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# OS files
Thumbs.db

# Ignore the build folder.
ReferenceFiles/

.swagger-mcp

# Test generated files
tests/generated/

# Ignore the test.json file
test.json

# Ignore my todo notepad file
TodoNotepad